# Task ID: 9
# Title: Implement Custom Prompt Editor Interface
# Status: done
# Dependencies: 8
# Priority: high
# Description: Build dual-prompt editor for Fact Extraction and Memory Evolution prompts with validation and syntax highlighting
# Details:
Create PromptEditor component using Monaco Editor or CodeMirror with syntax highlighting for prompt structure. Implement side-by-side editors for fact extraction and memory evolution prompts. Add real-time validation, character counters (4000 limit), auto-save every 30 seconds, version history, and reset to defaults functionality. Include import/export capabilities.

# Test Strategy:
Test syntax highlighting and validation, verify character limits and counters, test auto-save functionality, validate version history tracking, and test import/export features
