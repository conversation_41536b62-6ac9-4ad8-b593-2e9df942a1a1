"use client";

import { useState, useEffect } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { SaveIcon, RotateCcw, ArrowLeft, Settings, Brain, Code, TestTube, Cog } from "lucide-react";
import { useRouter } from "next/navigation";
import { useToast } from "@/components/ui/use-toast";
import { ProtectedRoute } from "@/components/protected-route";
import { OverviewTab } from "./components/OverviewTab";
import { DomainTab } from "./components/DomainTab";
import { PromptsTab } from "./components/PromptsTab";
import { TestingTab } from "./components/TestingTab";
import { AdvancedTab } from "./components/AdvancedTab";

export default function EvolutionSettingsPage() {
  const router = useRouter();
  const { toast } = useToast();
  const [activeTab, setActiveTab] = useState("overview");
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  // Handle tab changes with unsaved changes warning
  const handleTabChange = (newTab: string) => {
    if (hasUnsavedChanges) {
      if (confirm("You have unsaved changes. Are you sure you want to switch tabs?")) {
        setActiveTab(newTab);
        setHasUnsavedChanges(false);
      }
    } else {
      setActiveTab(newTab);
    }
  };

  // Handle navigation back to main settings
  const handleBackToSettings = () => {
    if (hasUnsavedChanges) {
      if (confirm("You have unsaved changes. Are you sure you want to leave?")) {
        router.push("/settings");
      }
    } else {
      router.push("/settings");
    }
  };

  // Handle save all configurations
  const handleSaveAll = async () => {
    setIsLoading(true);
    try {
      // This will be implemented to save all tab configurations
      await new Promise(resolve => setTimeout(resolve, 1000)); // Placeholder
      setHasUnsavedChanges(false);
      toast({
        title: "Settings saved",
        description: "Evolution configuration has been updated successfully.",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to save evolution configuration",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Handle reset to defaults
  const handleResetAll = async () => {
    if (confirm("This will reset all evolution settings to defaults. Are you sure?")) {
      setIsLoading(true);
      try {
        // This will be implemented to reset all configurations
        await new Promise(resolve => setTimeout(resolve, 1000)); // Placeholder
        setHasUnsavedChanges(false);
        toast({
          title: "Settings reset",
          description: "Evolution configuration has been reset to defaults.",
        });
      } catch (error) {
        toast({
          title: "Error",
          description: "Failed to reset evolution configuration",
          variant: "destructive",
        });
      } finally {
        setIsLoading(false);
      }
    }
  };

  return (
    <ProtectedRoute>
      <div className="text-white py-6">
        <div className="container mx-auto py-10 max-w-6xl">
          {/* Header with breadcrumb and actions */}
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-8 gap-4">
            <div className="animate-fade-slide-down">
              <div className="flex items-center gap-2 mb-2">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleBackToSettings}
                  className="text-zinc-400 hover:text-zinc-200 p-1"
                >
                  <ArrowLeft className="h-4 w-4" />
                </Button>
                <span className="text-zinc-400">Settings</span>
                <span className="text-zinc-600">/</span>
                <span className="text-zinc-200">Evolution</span>
              </div>
              <h1 className="text-3xl font-bold tracking-tight">Evolution Intelligence Settings</h1>
              <p className="text-muted-foreground mt-1">
                Configure memory evolution behavior, prompts, and optimization parameters
              </p>
            </div>
            
            <div className="flex space-x-2">
              <Button
                variant="outline"
                onClick={handleResetAll}
                disabled={isLoading}
                className="border-zinc-800 text-zinc-200 hover:bg-zinc-700 hover:text-zinc-50"
              >
                <RotateCcw className="mr-2 h-4 w-4" />
                Reset All
              </Button>
              <Button
                onClick={handleSaveAll}
                disabled={isLoading}
                className="bg-primary hover:bg-primary/90"
              >
                <SaveIcon className="mr-2 h-4 w-4" />
                {isLoading ? "Saving..." : "Save All"}
              </Button>
            </div>
          </div>

          {/* Unsaved changes indicator */}
          {hasUnsavedChanges && (
            <div className="mb-6 animate-fade-slide-down">
              <Badge variant="secondary" className="bg-yellow-900/20 text-yellow-400 border-yellow-800">
                You have unsaved changes
              </Badge>
            </div>
          )}

          {/* Tab Navigation */}
          <Tabs value={activeTab} onValueChange={handleTabChange} className="w-full animate-fade-slide-down delay-1">
            <TabsList className="grid w-full grid-cols-2 sm:grid-cols-5 mb-8 bg-zinc-900/50 border border-zinc-800">
              <TabsTrigger value="overview" className="flex items-center gap-2">
                <Settings className="h-4 w-4" />
                <span className="hidden sm:inline">Overview</span>
              </TabsTrigger>
              <TabsTrigger value="domain" className="flex items-center gap-2">
                <Brain className="h-4 w-4" />
                <span className="hidden sm:inline">Domain</span>
              </TabsTrigger>
              <TabsTrigger value="prompts" className="flex items-center gap-2">
                <Code className="h-4 w-4" />
                <span className="hidden sm:inline">Prompts</span>
              </TabsTrigger>
              <TabsTrigger value="testing" className="flex items-center gap-2">
                <TestTube className="h-4 w-4" />
                <span className="hidden sm:inline">Testing</span>
              </TabsTrigger>
              <TabsTrigger value="advanced" className="flex items-center gap-2">
                <Cog className="h-4 w-4" />
                <span className="hidden sm:inline">Advanced</span>
              </TabsTrigger>
            </TabsList>

            {/* Tab Content */}
            <TabsContent value="overview" className="space-y-6">
              <OverviewTab onSettingsChange={setHasUnsavedChanges} />
            </TabsContent>

            <TabsContent value="domain" className="space-y-6">
              <DomainTab onSettingsChange={setHasUnsavedChanges} />
            </TabsContent>

            <TabsContent value="prompts" className="space-y-6">
              <PromptsTab onSettingsChange={setHasUnsavedChanges} />
            </TabsContent>

            <TabsContent value="testing" className="space-y-6">
              <TestingTab onSettingsChange={setHasUnsavedChanges} />
            </TabsContent>

            <TabsContent value="advanced" className="space-y-6">
              <AdvancedTab onSettingsChange={setHasUnsavedChanges} />
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </ProtectedRoute>
  );
}
